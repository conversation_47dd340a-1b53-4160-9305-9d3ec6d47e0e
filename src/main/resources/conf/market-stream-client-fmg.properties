# would be nice if this would not be needed
fms.zookeeper.broker.list=use1-fozfd01-nxtbs1.dev.fndlsb.net:2181
fms.zookeeper.zNodePath=/fmgng/output/streamprotocol/metadata

fms.legacy-config=true
fms.kafka.broker.list=use1-fokfd01-intbs1.dev.fndlsb.net:9092
mvcache.topic.delta=sb_market_stream_ng_topic
#fms.kafka.broker.list=localhost:9092
#mvcache.topic.delta=old.market.stream
#fms.reactive.consumer.enable=false
#fms.metrics.service.enable=false
mvcache.listeners.queueSize=10
mvcache.listeners.numberOfShards=2
