package com.flutter.marketstreamcomparisontool.client;

import com.ppb.platform.sb.fmg.MarketStreamClient;
import com.ppb.platform.sb.toggle.feature.FeatureToggle;
import com.ppb.platform.sb.toggle.feature.SimpleFeatureToggle;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;


@Configuration
@ImportResource("classpath:conf/fms-new-client-application.xml")
public class MarketStreamConfiguration {

    @Bean
    public MarketStreamClient marketStreamClientGSSP(@Value("${market.stream.client.config.gssp}") final String marketStreamClientConfigGSSP,
                                                     @Value("${market.stream.consumer.config.gssp}") final String marketStreamConsumerConfigGSSP,
                                                     @Value("${market.stream.consumer.container.config.gssp}") final String marketStreamConsumerContainerConfigGSSP) throws Exception {
        return MarketStreamClientFactory.createClient(
                marketStreamClientConfigGSSP,
                marketStreamConsumerConfigGSSP,
                marketStreamConsumerContainerConfigGSSP
        );
    }

    @Bean
    public MarketStreamClient marketStreamClientFMG(@Value("${market.stream.client.config.fmg}") final String marketStreamClientConfigFMG,
                                                    @Value("${market.stream.consumer.config.fmg}") final String marketStreamConsumerConfigFMG,
                                                    @Value("${market.stream.consumer.container.config.fmg}") final String marketStreamConsumerContainerConfigFMG) throws Exception {
        return MarketStreamClientFactory.createClient(
                marketStreamClientConfigFMG,
                marketStreamConsumerConfigFMG,
                marketStreamConsumerContainerConfigFMG
        );
    }


    @Bean
    public FeatureToggle marketStreamClientToggleGSSP(@Value("${market.stream.client.enabled.gssp}") final boolean marketStreamClientEnabledGSSP) {
        return new SimpleFeatureToggle(marketStreamClientEnabledGSSP, "marketStreamClientGSSP");
    }

    @Bean
    public FeatureToggle marketStreamClientToggleFMG(@Value("${market.stream.client.enabled.fmg}") final boolean marketStreamClientEnabledFMG) {
        return new SimpleFeatureToggle(marketStreamClientEnabledFMG, "marketStreamClientFMG");
    }

}
